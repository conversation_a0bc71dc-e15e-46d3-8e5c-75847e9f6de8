<?php
/**
 * CFB Calculator Database Class
 * Handles all database operations for the plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Database {
    
    private static $instance = null;
    private $table_forms;
    private $table_form_fields;
    private $table_form_submissions;
    private $table_variables;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        global $wpdb;
        $this->table_forms = $wpdb->prefix . 'cfb_forms';
        $this->table_form_fields = $wpdb->prefix . 'cfb_form_fields';
        $this->table_form_submissions = $wpdb->prefix . 'cfb_form_submissions';
        $this->table_variables = $wpdb->prefix . 'cfb_variables';
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Forms table
        $sql_forms = "CREATE TABLE {$this->table_forms} (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            form_data longtext,
            settings longtext,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        // Form fields table
        $sql_fields = "CREATE TABLE {$this->table_form_fields} (
            id int(11) NOT NULL AUTO_INCREMENT,
            form_id int(11) NOT NULL,
            field_type varchar(50) NOT NULL,
            field_name varchar(255) NOT NULL,
            field_label varchar(255) NOT NULL,
            field_options longtext,
            field_settings longtext,
            conditional_logic longtext,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY form_id (form_id),
            KEY field_type (field_type)
        ) $charset_collate;";
        
        // Form submissions table
        $sql_submissions = "CREATE TABLE {$this->table_form_submissions} (
            id int(11) NOT NULL AUTO_INCREMENT,
            form_id int(11) NOT NULL,
            submission_data longtext,
            calculated_total decimal(10,2),
            user_ip varchar(45),
            user_agent text,
            submitted_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY form_id (form_id),
            KEY submitted_at (submitted_at)
        ) $charset_collate;";
        
        // Variables table
        $sql_variables = "CREATE TABLE {$this->table_variables} (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            label varchar(255) NOT NULL,
            value decimal(10,4) NOT NULL DEFAULT 0,
            description text,
            category varchar(100) DEFAULT 'general',
            icon varchar(50) DEFAULT 'dashicons-admin-settings',
            color varchar(7) DEFAULT '#667eea',
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY name (name),
            KEY category (category),
            KEY is_active (is_active)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_forms);
        dbDelta($sql_fields);
        dbDelta($sql_submissions);
        dbDelta($sql_variables);
    }
    
    /**
     * Get all forms
     */
    public function get_forms($status = 'active') {
        global $wpdb;
        
        $where = '';
        if ($status) {
            $where = $wpdb->prepare("WHERE status = %s", $status);
        }
        
        return $wpdb->get_results("SELECT * FROM {$this->table_forms} {$where} ORDER BY created_at DESC");
    }
    
    /**
     * Get form by ID
     */
    public function get_form($form_id) {
        global $wpdb;
        
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->table_forms} WHERE id = %d", $form_id));
    }
    
    /**
     * Save form
     */
    public function save_form($data) {
        global $wpdb;
        
        $form_data = array(
            'name' => sanitize_text_field($data['name']),
            'description' => sanitize_textarea_field($data['description']),
            'form_data' => wp_json_encode($data['form_data']),
            'settings' => wp_json_encode($data['settings']),
            'status' => sanitize_text_field($data['status'])
        );
        
        if (isset($data['id']) && $data['id']) {
            // Update existing form
            $wpdb->update(
                $this->table_forms,
                $form_data,
                array('id' => intval($data['id'])),
                array('%s', '%s', '%s', '%s', '%s'),
                array('%d')
            );
            return intval($data['id']);
        } else {
            // Insert new form
            $wpdb->insert(
                $this->table_forms,
                $form_data,
                array('%s', '%s', '%s', '%s', '%s')
            );
            return $wpdb->insert_id;
        }
    }
    
    /**
     * Delete form
     */
    public function delete_form($form_id) {
        global $wpdb;
        
        // Delete form fields
        $wpdb->delete($this->table_form_fields, array('form_id' => $form_id), array('%d'));
        
        // Delete form submissions
        $wpdb->delete($this->table_form_submissions, array('form_id' => $form_id), array('%d'));
        
        // Delete form
        return $wpdb->delete($this->table_forms, array('id' => $form_id), array('%d'));
    }
    
    /**
     * Get form fields
     */
    public function get_form_fields($form_id) {
        global $wpdb;
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$this->table_form_fields} WHERE form_id = %d ORDER BY sort_order ASC",
            $form_id
        ));
    }
    
    /**
     * Save form field
     */
    public function save_form_field($data) {
        global $wpdb;
        
        $field_data = array(
            'form_id' => intval($data['form_id']),
            'field_type' => sanitize_text_field($data['field_type']),
            'field_name' => sanitize_text_field($data['field_name']),
            'field_label' => sanitize_text_field($data['field_label']),
            'field_options' => wp_json_encode($data['field_options']),
            'field_settings' => wp_json_encode($data['field_settings']),
            'conditional_logic' => wp_json_encode($data['conditional_logic']),
            'sort_order' => intval($data['sort_order'])
        );
        
        if (isset($data['id']) && $data['id']) {
            // Update existing field
            $wpdb->update(
                $this->table_form_fields,
                $field_data,
                array('id' => intval($data['id'])),
                array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d'),
                array('%d')
            );
            return intval($data['id']);
        } else {
            // Insert new field
            $wpdb->insert(
                $this->table_form_fields,
                $field_data,
                array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d')
            );
            return $wpdb->insert_id;
        }
    }
    
    /**
     * Save form submission
     */
    public function save_submission($form_id, $submission_data, $calculated_total) {
        global $wpdb;
        
        return $wpdb->insert(
            $this->table_form_submissions,
            array(
                'form_id' => intval($form_id),
                'submission_data' => wp_json_encode($submission_data),
                'calculated_total' => floatval($calculated_total),
                'user_ip' => $this->get_user_ip(),
                'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'])
            ),
            array('%d', '%s', '%f', '%s', '%s')
        );
    }
    
    /**
     * Get user IP address
     */
    private function get_user_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Get form submissions
     */
    public function get_submissions($form_id, $limit = 50, $offset = 0) {
        global $wpdb;
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$this->table_form_submissions} 
             WHERE form_id = %d 
             ORDER BY submitted_at DESC 
             LIMIT %d OFFSET %d",
            $form_id, $limit, $offset
        ));
    }
    
    /**
     * Get submission count
     */
    public function get_submission_count($form_id) {
        global $wpdb;

        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->table_form_submissions} WHERE form_id = %d",
            $form_id
        ));
    }

    /**
     * Get all variables
     */
    public function get_variables($category = null, $active_only = true) {
        global $wpdb;

        $where = array();
        if ($active_only) {
            $where[] = "is_active = 1";
        }
        if ($category) {
            $where[] = $wpdb->prepare("category = %s", $category);
        }

        $where_clause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

        return $wpdb->get_results("SELECT * FROM {$this->table_variables} {$where_clause} ORDER BY category, name");
    }

    /**
     * Get variable by ID
     */
    public function get_variable($variable_id) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->table_variables} WHERE id = %d", $variable_id));
    }

    /**
     * Get variable by name
     */
    public function get_variable_by_name($name) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->table_variables} WHERE name = %s AND is_active = 1", $name));
    }

    /**
     * Save variable
     */
    public function save_variable($data) {
        global $wpdb;

        try {
            // Ensure variables table exists
            $this->ensure_variables_table_exists();

            // Validate required data
            if (empty($data['name']) || empty($data['label'])) {
                error_log('CFB Database: Missing required variable data');
                return false;
            }

            // Prepare variable data
            $variable_data = array(
                'name' => sanitize_text_field($data['name']),
                'label' => sanitize_text_field($data['label']),
                'value' => floatval($data['value']),
                'description' => sanitize_textarea_field($data['description'] ?? ''),
                'category' => sanitize_text_field($data['category'] ?? 'general'),
                'icon' => sanitize_text_field($data['icon'] ?? 'dashicons-admin-settings'),
                'color' => sanitize_hex_color($data['color'] ?? '#667eea'),
                'is_active' => isset($data['is_active']) ? intval($data['is_active']) : 1
            );

            error_log('CFB Database: Saving variable data: ' . print_r($variable_data, true));

            if (isset($data['id']) && $data['id']) {
                // Update existing variable
                $result = $wpdb->update(
                    $this->table_variables,
                    $variable_data,
                    array('id' => intval($data['id'])),
                    array('%s', '%s', '%f', '%s', '%s', '%s', '%s', '%d'),
                    array('%d')
                );

                if ($result === false) {
                    error_log('CFB Database: Update failed - ' . $wpdb->last_error);
                    return false;
                }

                error_log('CFB Database: Variable updated successfully - ID: ' . intval($data['id']));
                return intval($data['id']);
            } else {
                // Check for duplicate name
                $existing = $wpdb->get_var($wpdb->prepare(
                    "SELECT id FROM {$this->table_variables} WHERE name = %s",
                    $variable_data['name']
                ));

                if ($existing) {
                    error_log('CFB Database: Variable name already exists: ' . $variable_data['name']);
                    return false;
                }

                // Insert new variable
                $result = $wpdb->insert(
                    $this->table_variables,
                    $variable_data,
                    array('%s', '%s', '%f', '%s', '%s', '%s', '%s', '%d')
                );

                if ($result === false) {
                    error_log('CFB Database: Insert failed - ' . $wpdb->last_error);
                    return false;
                }

                $insert_id = $wpdb->insert_id;
                error_log('CFB Database: Variable inserted successfully - ID: ' . $insert_id);
                return $insert_id;
            }

        } catch (Exception $e) {
            error_log('CFB Database: Exception in save_variable - ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Ensure variables table exists
     */
    private function ensure_variables_table_exists() {
        global $wpdb;

        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->table_variables}'");

        if (!$table_exists) {
            error_log('CFB Database: Variables table missing, creating...');
            $this->create_tables();
        }
    }

    /**
     * Delete variable
     */
    public function delete_variable($variable_id) {
        global $wpdb;

        return $wpdb->delete($this->table_variables, array('id' => $variable_id), array('%d'));
    }

    /**
     * Get variable categories
     */
    public function get_variable_categories() {
        global $wpdb;

        return $wpdb->get_col("SELECT DISTINCT category FROM {$this->table_variables} WHERE is_active = 1 ORDER BY category");
    }
}
