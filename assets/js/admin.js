/**
 * CFB Calculator Admin JavaScript
 * Handles form builder, drag & drop, and admin interactions
 */

(function($) {
    'use strict';

    class CFBFormBuilder {
        constructor() {
            this.fieldCounter = 0;
            this.currentForm = {
                id: $('#form-id').val() || 0,
                name: '',
                description: '',
                fields: [],
                subtotals: [],
                settings: {}
            };
            
            this.init();
        }

        init() {
            this.bindEvents();
            this.initializeDragDrop();
            this.initializeTabs();
            this.initializeFormulaBuilder();
            this.loadExistingForm();
        }

        bindEvents() {
            // Save form
            $('#save-form').on('click', () => this.saveForm());
            
            // Preview form
            $('#preview-form').on('click', () => this.previewForm());
            
            // Add subtotal
            $('#add-subtotal').on('click', () => this.addSubtotal());
            
            // Remove subtotal
            $(document).on('click', '.cfb-remove-subtotal', (e) => {
                $(e.target).closest('.cfb-subtotal-editor').remove();
            });
            
            // Enable/disable subtotals
            $('#enable-subtotals').on('change', (e) => {
                const panel = $('#cfb-calculation-panel');
                if (e.target.checked) {
                    panel.show();
                } else {
                    panel.hide();
                }
            });
            
            // Field type drag start
            $('.cfb-field-type').on('dragstart', (e) => {
                e.originalEvent.dataTransfer.setData('text/plain', $(e.target).data('type'));
            });

            // One-click field addition
            $('.cfb-field-type').on('click', (e) => {
                const fieldType = $(e.target).closest('.cfb-field-type').data('type');
                if (fieldType) {
                    this.addField(fieldType);
                }
            });

            // Trigger fields update when field is added/removed/changed
            $(document).on('cfb-field-added cfb-field-removed cfb-field-changed', () => {
                $(document).trigger('cfb-fields-updated');
            });

            // Form list actions
            $(document).on('click', '.cfb-delete-form', (e) => this.deleteForm(e));
            $(document).on('click', '.cfb-duplicate-form', (e) => this.duplicateForm(e));
            $(document).on('click', '.cfb-toggle-status', (e) => this.toggleFormStatus(e));
            
            // Tab functionality
            $('.cfb-tab-button').on('click', (e) => this.switchTab(e));

            // Initialize formula builder
            this.initFormulaBuilder();
            
            // Settings form
            $('#cfb-settings-form').on('submit', (e) => this.saveSettings(e));
            
            // Reset settings
            $('#cfb-reset-settings').on('click', () => this.resetSettings());
        }

        initializeDragDrop() {
            const canvas = $('#cfb-form-fields');
            const dropZone = $('.cfb-drop-zone');

            // Make field types draggable
            $('.cfb-field-type').attr('draggable', true);

            // Canvas drop events
            canvas.on('dragover', (e) => {
                e.preventDefault();
                dropZone.addClass('drag-over');
            });

            canvas.on('dragleave', (e) => {
                if (!canvas[0].contains(e.relatedTarget)) {
                    dropZone.removeClass('drag-over');
                }
            });

            canvas.on('drop', (e) => {
                e.preventDefault();
                dropZone.removeClass('drag-over');
                
                const fieldType = e.originalEvent.dataTransfer.getData('text/plain');
                if (fieldType) {
                    this.addField(fieldType);
                }
            });

            // Make existing fields sortable
            canvas.sortable({
                handle: '.cfb-field-header',
                placeholder: 'cfb-field-placeholder',
                update: () => this.updateFieldOrder()
            });
        }

        initializeTabs() {
            // Settings page tabs
            $('.cfb-tab').on('click', function() {
                const tabId = $(this).data('tab');

                $('.cfb-tab').removeClass('active');
                $('.cfb-tab-content').removeClass('active');

                $(this).addClass('active');
                $(`#${tabId}-tab`).addClass('active');
            });
        }

        initializeFormulaBuilder() {
            // Initialize formula builder for total formula
            if ($('#total-formula-builder').length && window.CFBFormulaBuilder) {
                this.totalFormulaBuilder = new CFBFormulaBuilder('#total-formula-builder', {
                    fields: this.getAvailableFieldsForFormulas(),
                    variables: ['subtotal_1', 'subtotal_2', 'subtotal_3', 'subtotal_4', 'subtotal_5']
                });

                // Set initial value
                const initialValue = $('#total-formula-builder').data('value');
                if (initialValue) {
                    this.totalFormulaBuilder.setValue(initialValue);
                }

                // Sync with hidden textarea
                $('#total-formula-builder').on('input', '.cfb-formula-input', () => {
                    $('#total-formula').val(this.totalFormulaBuilder.getValue());
                });
            }

            // Initialize formula builders for subtotals
            this.initializeSubtotalFormulaBuilders();
        }

        initializeSubtotalFormulaBuilders() {
            $('.cfb-subtotal-editor').each((index, element) => {
                const subtotalEditor = $(element);
                const formulaTextarea = subtotalEditor.find('.subtotal-formula');

                if (formulaTextarea.length && !formulaTextarea.data('formula-builder-initialized')) {
                    const builderId = `subtotal-formula-builder-${index}`;
                    const builderHtml = `<div id="${builderId}" class="cfb-subtotal-formula-builder"></div>`;

                    formulaTextarea.after(builderHtml);
                    formulaTextarea.hide();

                    const builder = new CFBFormulaBuilder(`#${builderId}`, {
                        fields: this.getAvailableFieldsForFormulas(),
                        variables: []
                    });

                    builder.setValue(formulaTextarea.val());

                    // Sync with textarea
                    $(`#${builderId}`).on('input', '.cfb-formula-input', () => {
                        formulaTextarea.val(builder.getValue());
                    });

                    formulaTextarea.data('formula-builder-initialized', true);
                }
            });
        }

        getAvailableFieldsForFormulas() {
            const fields = [];
            $('#cfb-form-fields .cfb-field-editor').each((index, element) => {
                const fieldEditor = $(element);
                const fieldType = fieldEditor.data('field-type');
                const fieldName = fieldEditor.find('.field-name').val();
                const fieldLabel = fieldEditor.find('.field-label').val();

                if (fieldName && fieldLabel) {
                    fields.push({
                        name: fieldName,
                        label: fieldLabel,
                        type: fieldType
                    });
                }
            });
            return fields;
        }

        addField(fieldType) {
            this.fieldCounter++;
            const fieldId = `field_${this.fieldCounter}`;
            
            const fieldConfig = this.getDefaultFieldConfig(fieldType);
            fieldConfig.id = fieldId;
            fieldConfig.name = `${fieldType}_${this.fieldCounter}`;
            
            const fieldHtml = this.renderFieldEditor(fieldConfig);
            
            if ($('#cfb-form-fields .cfb-field-editor').length === 0) {
                $('.cfb-drop-zone').hide();
            }
            
            $('#cfb-form-fields').append(fieldHtml);

            // Initialize field settings
            this.initializeFieldSettings(fieldId, fieldConfig);

            // Trigger field added event
            $(document).trigger('cfb-field-added', [fieldConfig]);

            // Update available fields immediately
            setTimeout(() => {
                console.log('🔍 CFB Debug: Calling updateAvailableFields after field added');
                this.updateAvailableFields();
                this.updateFormulaBuilders();
            }, 100);
        }

        getDefaultFieldConfig(type) {
            const defaultConditionalLogic = {
                enabled: false,
                logic_type: 'all',
                conditions: []
            };

            const defaultLayout = {
                width: 'full',
                position: 'left'
            };

            const configs = {
                text: {
                    type: 'text',
                    label: 'Text Field',
                    placeholder: '',
                    required: false,
                    calculation_rule: { type: 'per_character', rate: 0 },
                    conditional_logic: defaultConditionalLogic,
                    ...defaultLayout
                },
                number: {
                    type: 'number',
                    label: 'Number Field',
                    min: 0,
                    max: 100,
                    step: 1,
                    default_value: 0,
                    required: false,
                    conditional_logic: defaultConditionalLogic,
                    ...defaultLayout
                },
                slider: {
                    type: 'slider',
                    label: 'Slider',
                    min: 0,
                    max: 100,
                    step: 1,
                    default_value: 50,
                    show_value: true,
                    conditional_logic: defaultConditionalLogic,
                    ...defaultLayout
                },
                dropdown: {
                    type: 'dropdown',
                    label: 'Dropdown',
                    placeholder: 'Select an option',
                    required: false,
                    options: [
                        { label: 'Option 1', value: 'option1', price: 0 },
                        { label: 'Option 2', value: 'option2', price: 0 }
                    ],
                    conditional_logic: defaultConditionalLogic,
                    ...defaultLayout
                },
                radio: {
                    type: 'radio',
                    label: 'Radio Buttons',
                    required: false,
                    options: [
                        { label: 'Option 1', value: 'option1', price: 0 },
                        { label: 'Option 2', value: 'option2', price: 0 }
                    ],
                    conditional_logic: defaultConditionalLogic,
                    ...defaultLayout
                },
                checkbox: {
                    type: 'checkbox',
                    label: 'Checkboxes',
                    options: [
                        { label: 'Option 1', value: 'option1', price: 0 },
                        { label: 'Option 2', value: 'option2', price: 0 }
                    ],
                    conditional_logic: defaultConditionalLogic,
                    ...defaultLayout
                },
                calculation: {
                    type: 'calculation',
                    label: 'Calculation Field',
                    formula: '',
                    display_type: 'number',
                    conditional_logic: defaultConditionalLogic,
                    ...defaultLayout
                },
                total: {
                    type: 'total',
                    label: 'Total',
                    formula: '',
                    display_type: 'currency',
                    show_breakdown: true,
                    conditional_logic: defaultConditionalLogic,
                    ...defaultLayout
                }
            };

            return configs[type] || {};
        }

        renderFieldEditor(field) {
            return `
                <div class="cfb-field-editor" data-field-id="${field.id}" data-field-type="${field.type}">
                    <div class="cfb-field-header">
                        <div class="cfb-field-move-controls">
                            <button type="button" class="cfb-move-field-up" title="Move Up">
                                <span class="dashicons dashicons-arrow-up-alt2"></span>
                            </button>
                            <button type="button" class="cfb-move-field-down" title="Move Down">
                                <span class="dashicons dashicons-arrow-down-alt2"></span>
                            </button>
                        </div>
                        <span class="cfb-field-title">${field.label}</span>
                        <div class="cfb-field-actions">
                            <button type="button" class="cfb-toggle-field" title="Toggle Settings">
                                <span class="dashicons dashicons-arrow-down-alt2"></span>
                            </button>
                            <button type="button" class="cfb-edit-field" title="Edit Field">
                                <span class="dashicons dashicons-edit"></span>
                            </button>
                            <button type="button" class="cfb-delete-field" title="Delete Field">
                                <span class="dashicons dashicons-trash"></span>
                            </button>
                        </div>
                    </div>
                    <div class="cfb-field-preview">
                        ${this.renderFieldPreview(field)}
                    </div>
                    <div class="cfb-field-settings" style="display: none;">
                        ${this.renderFieldSettings(field)}
                    </div>
                </div>
            `;
        }

        renderFieldPreview(field) {
            switch (field.type) {
                case 'text':
                    return `<input type="text" placeholder="${field.placeholder}" disabled>`;
                case 'number':
                    return `<input type="number" min="${field.min}" max="${field.max}" step="${field.step}" value="${field.default_value}" disabled>`;
                case 'slider':
                    return `<input type="range" min="${field.min}" max="${field.max}" step="${field.step}" value="${field.default_value}" disabled>`;
                case 'dropdown':
                    let options = field.options.map(opt => `<option value="${opt.value}">${opt.label}</option>`).join('');
                    return `<select disabled><option value="">${field.placeholder}</option>${options}</select>`;
                case 'radio':
                    return field.options.map(opt => 
                        `<label><input type="radio" name="preview_${field.id}" value="${opt.value}" disabled> ${opt.label}</label>`
                    ).join('<br>');
                case 'checkbox':
                    return field.options.map(opt =>
                        `<label><input type="checkbox" value="${opt.value}" disabled> ${opt.label}</label>`
                    ).join('<br>');
                case 'calculation':
                    return `<div class="cfb-calculation-preview">
                        <span class="dashicons dashicons-chart-line"></span>
                        Calculation Result: <strong>0</strong>
                    </div>`;
                case 'total':
                    return `<div class="cfb-total-preview">
                        <span class="dashicons dashicons-money-alt"></span>
                        Total: <strong>$0.00</strong>
                    </div>`;
                default:
                    return `<div class="cfb-unknown-field">
                        <span class="dashicons dashicons-admin-generic"></span>
                        ${field.type} field
                    </div>`;
            }
        }

        renderFieldSettings(field) {
            let html = `
                <div class="cfb-settings-grid">
                    <div class="cfb-settings-row">
                        <div class="cfb-form-group">
                            <label>Field Label</label>
                            <input type="text" class="field-label" value="${field.label}">
                        </div>
                        <div class="cfb-form-group">
                            <label>Field Name</label>
                            <input type="text" class="field-name" value="${field.name}">
                        </div>
                    </div>
            `;

            // Type-specific settings
            switch (field.type) {
                case 'text':
                    html += `
                        <div class="cfb-settings-row">
                            <div class="cfb-form-group">
                                <label>Placeholder</label>
                                <input type="text" class="field-placeholder" value="${field.placeholder}">
                            </div>
                            <div class="cfb-form-group">
                                <label class="cfb-checkbox-label">
                                    <input type="checkbox" class="field-required" ${field.required ? 'checked' : ''}>
                                    <span>Required</span>
                                </label>
                            </div>
                        </div>
                    `;
                    break;
                case 'number':
                case 'slider':
                    html += `
                        <div class="cfb-settings-row">
                            <div class="cfb-form-group">
                                <label>Minimum Value</label>
                                <input type="number" class="field-min" value="${field.min}">
                            </div>
                            <div class="cfb-form-group">
                                <label>Maximum Value</label>
                                <input type="number" class="field-max" value="${field.max}">
                            </div>
                        </div>
                        <div class="cfb-settings-row">
                            <div class="cfb-form-group">
                                <label>Step</label>
                                <input type="number" class="field-step" value="${field.step}" step="0.01">
                            </div>
                            <div class="cfb-form-group">
                                <label>Default Value</label>
                                <input type="number" class="field-default" value="${field.default_value}">
                            </div>
                        </div>
                    `;
                    if (field.type === 'slider') {
                        html += `
                            <div class="cfb-form-group">
                                <label>
                                    <input type="checkbox" class="field-show-value" ${field.show_value ? 'checked' : ''}>
                                    Show Value
                                </label>
                            </div>
                        `;
                    }
                    break;
                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    html += `
                        <div class="cfb-form-group">
                            <label>Options</label>
                            <div class="cfb-options-editor">
                                ${this.renderOptionsEditor(field.options)}
                            </div>
                            <button type="button" class="cfb-add-option">Add Option</button>
                        </div>
                    `;
                    break;
                case 'calculation':
                case 'total':
                    html += `
                        <div class="cfb-form-group">
                            <label>Display Type</label>
                            <select class="field-display-type">
                                <option value="number" ${field.display_type === 'number' ? 'selected' : ''}>Number</option>
                                <option value="currency" ${field.display_type === 'currency' ? 'selected' : ''}>Currency</option>
                                <option value="percentage" ${field.display_type === 'percentage' ? 'selected' : ''}>Percentage</option>
                            </select>
                        </div>
                        <div class="cfb-field-setting cfb-formula-setting">
                            <label>Formula</label>
                            <div class="cfb-field-formula-container" data-field-type="${field.type}">
                                <!-- CFBFieldFormulaBuilder will be initialized here -->
                            </div>
                        </div>
                    `;
                    if (field.type === 'total') {
                        html += `
                            <div class="cfb-form-group">
                                <label>
                                    <input type="checkbox" class="field-show-breakdown" ${field.show_breakdown ? 'checked' : ''}>
                                    Show calculation breakdown
                                </label>
                            </div>
                        `;
                    }
                    break;
            }

            html += `
                    <div class="cfb-settings-row">
                        <div class="cfb-form-group">
                            <label>Width</label>
                            <select class="field-width">
                                <option value="full" ${field.width === 'full' ? 'selected' : ''}>Full Width (100%)</option>
                                <option value="half" ${field.width === 'half' ? 'selected' : ''}>Half Width (50%)</option>
                                <option value="third" ${field.width === 'third' ? 'selected' : ''}>One Third (33%)</option>
                                <option value="quarter" ${field.width === 'quarter' ? 'selected' : ''}>Quarter (25%)</option>
                            </select>
                        </div>
                        <div class="cfb-form-group">
                            <label>Position</label>
                            <select class="field-position">
                                <option value="left" ${field.position === 'left' ? 'selected' : ''}>Left</option>
                                <option value="center" ${field.position === 'center' ? 'selected' : ''}>Center</option>
                                <option value="right" ${field.position === 'right' ? 'selected' : ''}>Right</option>
                            </select>
                        </div>
                    </div>

                    <div class="cfb-form-group">
                        <h4>Conditional Logic</h4>
                        <label class="cfb-checkbox-label">
                            <input type="checkbox" class="enable-conditional" ${field.conditional_logic?.enabled ? 'checked' : ''}>
                            <span>Enable conditional logic</span>
                        </label>
                        <div class="cfb-conditional-logic" style="display: ${field.conditional_logic?.enabled ? 'block' : 'none'};">
                            ${this.renderConditionalLogicBuilder(field.conditional_logic || {})}
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        renderOptionsEditor(options) {
            return options.map((option, index) => `
                <div class="cfb-option-item">
                    <input type="text" placeholder="Label" value="${option.label}" class="option-label">
                    <input type="text" placeholder="Value" value="${option.value}" class="option-value">
                    <input type="number" placeholder="Price" value="${option.price}" class="option-price" step="0.01">
                    <button type="button" class="cfb-remove-option">Remove</button>
                </div>
            `).join('');
        }

        renderConditionalLogicBuilder(conditionalLogic) {
            const conditions = conditionalLogic.conditions || [];
            const logicType = conditionalLogic.logic_type || 'all';

            let html = `
                <div class="cfb-conditional-settings">
                    <div class="cfb-form-group">
                        <label>Show this field if:</label>
                        <select class="conditional-logic-type">
                            <option value="all" ${logicType === 'all' ? 'selected' : ''}>All conditions are met</option>
                            <option value="any" ${logicType === 'any' ? 'selected' : ''}>Any condition is met</option>
                        </select>
                    </div>

                    <div class="cfb-conditions-list">
            `;

            if (conditions.length === 0) {
                html += this.renderConditionRow({});
            } else {
                conditions.forEach((condition, index) => {
                    html += this.renderConditionRow(condition, index);
                });
            }

            html += `
                    </div>

                    <button type="button" class="cfb-add-condition button">Add Condition</button>
                </div>
            `;

            return html;
        }

        renderConditionRow(condition, index = 0) {
            const availableFields = this.getAvailableFieldsForConditions();

            return `
                <div class="cfb-condition-row" data-index="${index}">
                    <div class="cfb-condition-field">
                        <select class="condition-field">
                            <option value="">Select Field</option>
                            ${availableFields.map(field =>
                                `<option value="${field.name}" ${condition.field === field.name ? 'selected' : ''}>${field.label}</option>`
                            ).join('')}
                        </select>
                    </div>

                    <div class="cfb-condition-operator">
                        <select class="condition-operator">
                            <option value="equals" ${condition.operator === 'equals' ? 'selected' : ''}>equals</option>
                            <option value="not_equals" ${condition.operator === 'not_equals' ? 'selected' : ''}>not equals</option>
                            <option value="greater_than" ${condition.operator === 'greater_than' ? 'selected' : ''}>greater than</option>
                            <option value="less_than" ${condition.operator === 'less_than' ? 'selected' : ''}>less than</option>
                            <option value="contains" ${condition.operator === 'contains' ? 'selected' : ''}>contains</option>
                            <option value="not_empty" ${condition.operator === 'not_empty' ? 'selected' : ''}>is not empty</option>
                            <option value="empty" ${condition.operator === 'empty' ? 'selected' : ''}>is empty</option>
                        </select>
                    </div>

                    <div class="cfb-condition-value">
                        <input type="text" class="condition-value" value="${condition.value || ''}" placeholder="Value">
                    </div>

                    <div class="cfb-condition-actions">
                        <button type="button" class="cfb-remove-condition" title="Remove condition">×</button>
                    </div>
                </div>
            `;
        }

        getAvailableFieldsForConditions() {
            const fields = [];
            $('#cfb-form-fields .cfb-field-editor').each((index, element) => {
                const fieldEditor = $(element);
                const fieldType = fieldEditor.data('field-type');
                const fieldName = fieldEditor.find('.field-name').val();
                const fieldLabel = fieldEditor.find('.field-label').val();

                if (fieldName && fieldLabel) {
                    fields.push({
                        name: fieldName,
                        label: fieldLabel,
                        type: fieldType
                    });
                }
            });
            return fields;
        }

        initializeFieldSettings(fieldId, fieldConfig) {
            const fieldEditor = $(`.cfb-field-editor[data-field-id="${fieldId}"]`);
            
            // Edit button
            fieldEditor.find('.cfb-edit-field').on('click', () => {
                const settings = fieldEditor.find('.cfb-field-settings');
                settings.toggle();
            });
            
            // Toggle button
            fieldEditor.find('.cfb-toggle-field').on('click', () => {
                const settings = fieldEditor.find('.cfb-field-settings');
                const toggleBtn = fieldEditor.find('.cfb-toggle-field');
                settings.slideToggle();
                toggleBtn.toggleClass('active');

                // If this is a total field and settings are being opened, update available fields
                if (fieldEditor.data('field-type') === 'total' && !toggleBtn.hasClass('active')) {
                    setTimeout(() => {
                        console.log('🔍 CFB Debug: Total field opened, updating available fields');
                        this.updateAvailableFields();
                    }, 300);
                }
            });

            // Delete button
            fieldEditor.find('.cfb-delete-field').on('click', () => {
                fieldEditor.remove();
                if ($('#cfb-form-fields .cfb-field-editor').length === 0) {
                    $('.cfb-drop-zone').show();
                }
                $(document).trigger('cfb-field-removed');
                $(document).trigger('cfb-fields-updated');
            });

            // Move up button
            fieldEditor.find('.cfb-move-field-up').on('click', () => {
                const prev = fieldEditor.prev('.cfb-field-editor');
                if (prev.length) {
                    fieldEditor.insertBefore(prev);
                    $(document).trigger('cfb-field-moved');
                    $(document).trigger('cfb-fields-updated');
                }
            });

            // Move down button
            fieldEditor.find('.cfb-move-field-down').on('click', () => {
                const next = fieldEditor.next('.cfb-field-editor');
                if (next.length) {
                    fieldEditor.insertAfter(next);
                    $(document).trigger('cfb-field-moved');
                    $(document).trigger('cfb-fields-updated');
                }
            });
            
            // Settings change handlers
            fieldEditor.find('.field-label').on('input', (e) => {
                fieldEditor.find('.cfb-field-title').text(e.target.value);
                // Refresh condition field options when field labels change
                this.refreshConditionFieldOptions();
                // Update available fields in formula builders
                this.updateAvailableFields();
                // Trigger field change event to update formula builders
                $(document).trigger('cfb-fields-updated');
            });

            fieldEditor.find('.field-name').on('input', () => {
                // Refresh condition field options when field names change
                this.refreshConditionFieldOptions();
                // Update available fields in formula builders
                this.updateAvailableFields();
                // Trigger field change event to update formula builders
                $(document).trigger('cfb-fields-updated');
            });
            
            // Add option button
            fieldEditor.find('.cfb-add-option').on('click', () => {
                const optionsEditor = fieldEditor.find('.cfb-options-editor');
                const newOption = `
                    <div class="cfb-option-item">
                        <input type="text" placeholder="Label" class="option-label">
                        <input type="text" placeholder="Value" class="option-value">
                        <input type="number" placeholder="Price" class="option-price" step="0.01">
                        <button type="button" class="cfb-remove-option">Remove</button>
                    </div>
                `;
                optionsEditor.append(newOption);
            });
            
            // Remove option button
            fieldEditor.on('click', '.cfb-remove-option', (e) => {
                $(e.target).closest('.cfb-option-item').remove();
            });

            // Initialize formula builder for calculation/total fields
            const formulaContainer = fieldEditor.find('.cfb-field-formula-container');
            if (formulaContainer.length && window.CFBFieldFormulaBuilder) {
                const fieldType = formulaContainer.data('field-type');
                const formulaBuilder = new CFBFieldFormulaBuilder(formulaContainer[0], {
                    fields: this.getAvailableFieldsForFormulas(),
                    fieldType: fieldType
                });

                // Set initial value if exists
                if (fieldConfig.formula) {
                    formulaBuilder.setValue(fieldConfig.formula);
                }

                // Store reference for later updates
                formulaContainer.data('formula-builder', formulaBuilder);
            }

            // Conditional logic toggle
            fieldEditor.find('.enable-conditional').on('change', (e) => {
                const conditionalPanel = fieldEditor.find('.cfb-conditional-logic');
                if (e.target.checked) {
                    conditionalPanel.show();
                    if (conditionalPanel.find('.cfb-conditions-list').children().length === 0) {
                        conditionalPanel.find('.cfb-conditions-list').html(this.renderConditionRow({}));
                    }
                } else {
                    conditionalPanel.hide();
                }
            });

            // Add condition button
            fieldEditor.on('click', '.cfb-add-condition', () => {
                const conditionsList = fieldEditor.find('.cfb-conditions-list');
                const newIndex = conditionsList.children().length;
                conditionsList.append(this.renderConditionRow({}, newIndex));
            });

            // Remove condition button
            fieldEditor.on('click', '.cfb-remove-condition', (e) => {
                const conditionRow = $(e.target).closest('.cfb-condition-row');
                const conditionsList = conditionRow.parent();

                conditionRow.remove();

                // Ensure at least one condition exists if conditional logic is enabled
                if (conditionsList.children().length === 0 && fieldEditor.find('.enable-conditional').is(':checked')) {
                    conditionsList.append(this.renderConditionRow({}));
                }
            });

            // Update condition field options when field changes
            fieldEditor.on('change', '.condition-field', (e) => {
                const selectedField = $(e.target).val();
                const conditionRow = $(e.target).closest('.cfb-condition-row');
                const valueInput = conditionRow.find('.condition-value');

                // Update value input based on field type
                this.updateConditionValueInput(selectedField, valueInput);
            });

            // Hide value input for certain operators
            fieldEditor.on('change', '.condition-operator', (e) => {
                const operator = $(e.target).val();
                const valueDiv = $(e.target).closest('.cfb-condition-row').find('.cfb-condition-value');

                if (operator === 'not_empty' || operator === 'empty') {
                    valueDiv.hide();
                } else {
                    valueDiv.show();
                }
            });
        }

        updateConditionValueInput(fieldName, valueInput) {
            // Find the field configuration
            const fieldEditor = $(`.cfb-field-editor`).find('.field-name').filter(function() {
                return $(this).val() === fieldName;
            }).closest('.cfb-field-editor');

            if (fieldEditor.length === 0) {
                valueInput.replaceWith('<input type="text" class="condition-value" placeholder="Value">');
                return;
            }

            const fieldType = fieldEditor.data('field-type');

            switch (fieldType) {
                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    // Create select with field options
                    const options = [];
                    fieldEditor.find('.cfb-option-item').each(function() {
                        const label = $(this).find('.option-label').val();
                        const value = $(this).find('.option-value').val();
                        if (label && value) {
                            options.push({ label, value });
                        }
                    });

                    let selectHtml = '<select class="condition-value"><option value="">Select value</option>';
                    options.forEach(option => {
                        selectHtml += `<option value="${option.value}">${option.label}</option>`;
                    });
                    selectHtml += '</select>';

                    valueInput.replaceWith(selectHtml);
                    break;

                case 'number':
                case 'slider':
                    valueInput.replaceWith('<input type="number" class="condition-value" placeholder="Number">');
                    break;

                default:
                    valueInput.replaceWith('<input type="text" class="condition-value" placeholder="Value">');
                    break;
            }
        }

        // Removed addSubtotal - now using calculation field types

        updateFieldOrder() {
            // Update field order when fields are reordered
            $('#cfb-form-fields .cfb-field-editor').each((index, element) => {
                $(element).attr('data-order', index);
            });
        }

        updateFormulaBuilders() {
            // Update all formula builders with current field list
            const fields = this.getAvailableFieldsForFormulas();

            if (this.totalFormulaBuilder) {
                this.totalFormulaBuilder.updateFields(fields);
            }

            // Update subtotal formula builders
            $('.cfb-subtotal-formula-builder').each((index, element) => {
                const builder = $(element).data('cfb-formula-builder');
                if (builder) {
                    builder.updateFields(fields);
                }
            });

            // Update individual field formula builders (calculation and total fields)
            $('.cfb-field-formula-container').each((index, element) => {
                const formulaBuilder = $(element).data('formula-builder');
                if (formulaBuilder && formulaBuilder.updateFields) {
                    formulaBuilder.updateFields(fields);
                }
            });
        }

        saveForm() {
            const formData = this.collectFormData();
            
            $.ajax({
                url: cfb_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_save_form',
                    nonce: cfb_admin_ajax.nonce,
                    form_id: this.currentForm.id,
                    form_data: formData
                },
                success: (response) => {
                    if (response.success) {
                        this.showMessage('Form saved successfully!', 'success');
                        if (!this.currentForm.id) {
                            this.currentForm.id = response.data.form_id;
                            $('#form-id').val(this.currentForm.id);
                        }
                    } else {
                        this.showMessage(response.data || 'Failed to save form', 'error');
                    }
                },
                error: () => {
                    this.showMessage('Network error occurred', 'error');
                }
            });
        }

        collectFormData() {
            const data = {
                name: $('#form-name').val(),
                description: $('#form-description').val(),
                save_submissions: $('#save-submissions').is(':checked'),
                fields: [],
                settings: {}
            };

            // Collect fields
            $('#cfb-form-fields .cfb-field-editor').each((index, element) => {
                const fieldEditor = $(element);
                const fieldData = this.collectFieldData(fieldEditor);
                if (fieldData) {
                    data.fields.push(fieldData);
                }
            });

            return data;
        }

        collectFieldData(fieldEditor) {
            const fieldType = fieldEditor.data('field-type');
            const settings = fieldEditor.find('.cfb-field-settings');

            const data = {
                type: fieldType,
                label: settings.find('.field-label').val(),
                name: settings.find('.field-name').val(),
                required: settings.find('.field-required').is(':checked'),
                width: settings.find('.field-width').val() || 'full',
                position: settings.find('.field-position').val() || 'left'
            };

            // Type-specific data collection
            switch (fieldType) {
                case 'text':
                    data.placeholder = settings.find('.field-placeholder').val();
                    break;
                case 'number':
                case 'slider':
                    data.min = parseFloat(settings.find('.field-min').val()) || 0;
                    data.max = parseFloat(settings.find('.field-max').val()) || 100;
                    data.step = parseFloat(settings.find('.field-step').val()) || 1;
                    data.default_value = parseFloat(settings.find('.field-default').val()) || 0;
                    if (fieldType === 'slider') {
                        data.show_value = settings.find('.field-show-value').is(':checked');
                    }
                    break;
                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    data.options = [];
                    settings.find('.cfb-option-item').each((index, element) => {
                        const option = $(element);
                        const optionData = {
                            label: option.find('.option-label').val(),
                            value: option.find('.option-value').val(),
                            price: parseFloat(option.find('.option-price').val()) || 0
                        };
                        if (optionData.label && optionData.value) {
                            data.options.push(optionData);
                        }
                    });
                    break;
                case 'calculation':
                case 'total':
                    data.display_type = settings.find('.field-display-type').val() || 'number';

                    // Get formula from formula builder
                    const formulaContainer = settings.find('.cfb-field-formula-container');
                    const formulaBuilder = formulaContainer.data('formula-builder');
                    if (formulaBuilder) {
                        data.formula = formulaBuilder.getValue();
                    }

                    if (fieldType === 'total') {
                        data.show_breakdown = settings.find('.field-show-breakdown').is(':checked');
                    }
                    break;
            }

            // Collect conditional logic data
            const conditionalEnabled = settings.find('.enable-conditional').is(':checked');
            data.conditional_logic = {
                enabled: conditionalEnabled
            };

            if (conditionalEnabled) {
                data.conditional_logic.logic_type = settings.find('.conditional-logic-type').val() || 'all';
                data.conditional_logic.conditions = [];

                settings.find('.cfb-condition-row').each((index, element) => {
                    const conditionRow = $(element);
                    const condition = {
                        field: conditionRow.find('.condition-field').val(),
                        operator: conditionRow.find('.condition-operator').val(),
                        value: conditionRow.find('.condition-value').val()
                    };

                    // Only add condition if field and operator are selected
                    if (condition.field && condition.operator) {
                        // For operators that don't need a value, set empty string
                        if (condition.operator === 'not_empty' || condition.operator === 'empty') {
                            condition.value = '';
                        }
                        data.conditional_logic.conditions.push(condition);
                    }
                });
            }

            return data;
        }

        loadExistingForm() {
            // Load existing form data if editing
            const formId = $('#form-id').val();
            if (formId && formId !== '0' && window.cfbFormData && window.cfbFormInfo) {
                console.log('Loading existing form:', formId);

                // Load basic form info
                if (window.cfbFormInfo.name) {
                    $('#form-name').val(window.cfbFormInfo.name);
                }
                if (window.cfbFormInfo.description) {
                    $('#form-description').val(window.cfbFormInfo.description);
                }

                // Load form settings
                if (window.cfbFormData.save_submissions) {
                    $('#save-submissions').prop('checked', true);
                }

                // Load fields
                if (window.cfbFormData.fields && Array.isArray(window.cfbFormData.fields)) {
                    $('.cfb-drop-zone').hide();
                    window.cfbFormData.fields.forEach((field, index) => {
                        this.loadExistingField(field, index);
                    });

                    // Update formula builders after all fields are loaded
                    setTimeout(() => {
                        this.updateFormulaBuilders();
                    }, 200);
                }

                // Subtotal loading removed - now using calculation field types
            }
        }

        loadExistingField(fieldData, index) {
            this.fieldCounter = Math.max(this.fieldCounter, index + 1);
            const fieldId = `field_${this.fieldCounter}`;

            // Set the field ID and name
            fieldData.id = fieldId;
            if (!fieldData.name) {
                fieldData.name = `${fieldData.type}_${this.fieldCounter}`;
            }

            // Create field with existing data
            const fieldHtml = this.renderFieldEditor(fieldData);
            $('#cfb-form-fields').append(fieldHtml);

            // Initialize field settings with existing data
            const fieldEditor = $(`.cfb-field-editor[data-field-id="${fieldId}"]`);
            this.populateFieldSettings(fieldEditor, fieldData);
            this.initializeFieldSettings(fieldId, fieldData);
        }

        populateFieldSettings(fieldEditor, fieldData) {
            const settings = fieldEditor.find('.cfb-field-settings');

            // Basic settings
            settings.find('.field-label').val(fieldData.label || '');
            settings.find('.field-name').val(fieldData.name || '');
            settings.find('.field-required').prop('checked', fieldData.required || false);

            // Type-specific settings
            switch (fieldData.type) {
                case 'text':
                    settings.find('.field-placeholder').val(fieldData.placeholder || '');
                    break;
                case 'number':
                case 'slider':
                    settings.find('.field-min').val(fieldData.min || 0);
                    settings.find('.field-max').val(fieldData.max || 100);
                    settings.find('.field-step').val(fieldData.step || 1);
                    settings.find('.field-default').val(fieldData.default_value || 0);
                    if (fieldData.type === 'slider') {
                        settings.find('.field-show-value').prop('checked', fieldData.show_value || false);
                    }
                    break;
                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    if (fieldData.options && Array.isArray(fieldData.options)) {
                        const optionsEditor = settings.find('.cfb-options-editor');
                        optionsEditor.empty();
                        fieldData.options.forEach(option => {
                            const optionHtml = `
                                <div class="cfb-option-item">
                                    <input type="text" placeholder="Label" value="${option.label || ''}" class="option-label">
                                    <input type="text" placeholder="Value" value="${option.value || ''}" class="option-value">
                                    <input type="number" placeholder="Price" value="${option.price || 0}" class="option-price" step="0.01">
                                    <button type="button" class="cfb-remove-option">Remove</button>
                                </div>
                            `;
                            optionsEditor.append(optionHtml);
                        });
                    }
                    break;
            }

            // Conditional logic
            if (fieldData.conditional_logic && fieldData.conditional_logic.enabled) {
                settings.find('.enable-conditional').prop('checked', true);
                const conditionalPanel = settings.find('.cfb-conditional-logic');
                conditionalPanel.show();
                conditionalPanel.html(this.renderConditionalLogicBuilder(fieldData.conditional_logic));
            }
        }

        // Removed loadExistingSubtotal - now using calculation field types

        // Helper method to refresh condition field options
        refreshConditionFieldOptions() {
            $('.cfb-conditional-logic .condition-field').each((index, element) => {
                const currentValue = $(element).val();
                const availableFields = this.getAvailableFieldsForConditions();

                let optionsHtml = '<option value="">Select Field</option>';
                availableFields.forEach(field => {
                    const selected = field.name === currentValue ? 'selected' : '';
                    optionsHtml += `<option value="${field.name}" ${selected}>${field.label}</option>`;
                });

                $(element).html(optionsHtml);
            });
        }

        deleteForm(e) {
            e.preventDefault();

            if (!confirm('Are you sure you want to delete this form? This action cannot be undone.')) {
                return;
            }

            const formId = $(e.target).closest('.cfb-delete-form').data('form-id');

            $.ajax({
                url: cfb_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_delete_form',
                    nonce: cfb_admin_ajax.nonce,
                    form_id: formId
                },
                success: (response) => {
                    if (response.success) {
                        $(e.target).closest('.cfb-form-card').fadeOut(() => {
                            $(e.target).closest('.cfb-form-card').remove();
                        });
                        this.showMessage('Form deleted successfully!', 'success');
                    } else {
                        this.showMessage(response.data || 'Failed to delete form', 'error');
                    }
                },
                error: () => {
                    this.showMessage('Network error occurred', 'error');
                }
            });
        }

        duplicateForm(e) {
            e.preventDefault();

            const formId = $(e.target).closest('.cfb-duplicate-form').data('form-id');

            $.ajax({
                url: cfb_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_duplicate_form',
                    nonce: cfb_admin_ajax.nonce,
                    form_id: formId
                },
                success: (response) => {
                    if (response.success) {
                        this.showMessage('Form duplicated successfully!', 'success');
                        location.reload(); // Reload to show the new form
                    } else {
                        this.showMessage(response.data || 'Failed to duplicate form', 'error');
                    }
                },
                error: () => {
                    this.showMessage('Network error occurred', 'error');
                }
            });
        }

        toggleFormStatus(e) {
            e.preventDefault();

            const button = $(e.target).closest('.cfb-toggle-status');
            const formId = button.data('form-id');
            const currentStatus = button.data('status');

            $.ajax({
                url: cfb_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_toggle_form_status',
                    nonce: cfb_admin_ajax.nonce,
                    form_id: formId,
                    current_status: currentStatus
                },
                success: (response) => {
                    if (response.success) {
                        const newStatus = response.data.new_status;
                        button.data('status', newStatus);
                        button.text(newStatus === 'active' ? 'Deactivate' : 'Activate');

                        const statusBadge = button.closest('.cfb-form-card').find('.cfb-status-badge');
                        statusBadge.removeClass('cfb-status-active cfb-status-inactive');
                        statusBadge.addClass('cfb-status-' + newStatus);
                        statusBadge.text(newStatus === 'active' ? 'Active' : 'Inactive');

                        this.showMessage(response.data.message, 'success');
                    } else {
                        this.showMessage(response.data || 'Failed to update form status', 'error');
                    }
                },
                error: () => {
                    this.showMessage('Network error occurred', 'error');
                }
            });
        }

        switchTab(e) {
            e.preventDefault();

            const clickedTab = $(e.currentTarget);
            const targetTab = clickedTab.data('tab');

            // Remove active class from all tabs and content
            $('.cfb-tab-button').removeClass('active');
            $('.cfb-tab-content').removeClass('active');

            // Add active class to clicked tab and corresponding content
            clickedTab.addClass('active');
            $(`#cfb-tab-${targetTab}`).addClass('active');
        }

        showMessage(message, type) {
            const messageHtml = `<div class="cfb-message ${type}">${message}</div>`;
            $('.wrap').prepend(messageHtml);

            setTimeout(() => {
                $('.cfb-message').fadeOut(() => {
                    $('.cfb-message').remove();
                });
            }, 3000);
        }

        /**
         * Initialize formula builder functionality
         */
        initFormulaBuilder() {
            // Click to insert formula items
            $(document).on('click', '.cfb-formula-item', (e) => {
                const item = $(e.currentTarget);
                const insertText = item.data('insert');
                const textarea = item.closest('.cfb-formula-builder').find('.cfb-formula-input');

                if (insertText && textarea.length) {
                    this.insertAtCursor(textarea[0], insertText);
                    textarea.focus();
                }
            });

            // Update available fields when fields change
            $(document).on('cfb-fields-updated', () => {
                this.updateAvailableFields();
                this.updateFormulaBuilders(); // Also update formula builders
            });

            // Initialize on page load
            this.updateAvailableFields();
        }

        /**
         * Insert text at cursor position in textarea
         */
        insertAtCursor(textarea, text) {
            const startPos = textarea.selectionStart;
            const endPos = textarea.selectionEnd;
            const value = textarea.value;

            textarea.value = value.substring(0, startPos) + text + value.substring(endPos);
            textarea.selectionStart = textarea.selectionEnd = startPos + text.length;

            // Trigger change event
            $(textarea).trigger('change');
        }

        /**
         * Update available fields in formula builder
         */
        updateAvailableFields() {
            console.log('🔍 CFB Debug: updateAvailableFields called');

            const fieldsContainer = $('.cfb-available-fields');
            console.log('🔍 CFB Debug: Fields container found:', fieldsContainer.length);

            if (!fieldsContainer.length) {
                console.log('❌ CFB Debug: No fields container found');
                return;
            }

            const fields = this.getFormFields();
            console.log('🔍 CFB Debug: Fields returned:', fields);

            if (fields.length === 0) {
                console.log('❌ CFB Debug: No fields found, showing placeholder');
                fieldsContainer.html('<p class="cfb-no-items">Add fields to see them here</p>');
                return;
            }

            console.log('✅ CFB Debug: Building HTML for', fields.length, 'fields');
            let fieldsHtml = '';
            fields.forEach(field => {
                const icon = this.getFieldIcon(field.type);
                fieldsHtml += `
                    <div class="cfb-formula-item cfb-field-item" data-insert="{${field.name}}" title="${field.label}">
                        <span class="cfb-item-icon dashicons ${icon}"></span>
                        <div class="cfb-item-content">
                            <span class="cfb-item-name">${field.label}</span>
                            <span class="cfb-item-code">{${field.name}}</span>
                        </div>
                    </div>
                `;
            });

            console.log('🔍 CFB Debug: Generated HTML length:', fieldsHtml.length);
            fieldsContainer.html(fieldsHtml);
            console.log('✅ CFB Debug: Fields HTML updated');
        }

        /**
         * Get current form fields
         */
        getFormFields() {
            console.log('🔍 CFB Debug: getFormFields called');
            const fields = [];

            const fieldEditors = $('.cfb-field-editor');
            console.log('🔍 CFB Debug: Found field editors:', fieldEditors.length);

            fieldEditors.each(function(index) {
                const fieldEditor = $(this);
                const fieldType = fieldEditor.data('field-type');

                console.log(`🔍 CFB Debug: Field ${index + 1}:`, {
                    element: fieldEditor[0],
                    type: fieldType,
                    hasNameInput: fieldEditor.find('.field-name').length,
                    hasLabelInput: fieldEditor.find('.field-label').length
                });

                // Get field data from the settings inputs
                const fieldName = fieldEditor.find('.field-name').val();
                const fieldLabel = fieldEditor.find('.field-label').val();

                console.log(`🔍 CFB Debug: Field ${index + 1} values:`, {
                    name: fieldName,
                    label: fieldLabel,
                    type: fieldType
                });

                if (fieldName && fieldLabel) {
                    // Exclude calculation and total fields from being referenced
                    if (!['calculation', 'total'].includes(fieldType)) {
                        fields.push({
                            name: fieldName,
                            label: fieldLabel,
                            type: fieldType
                        });
                        console.log(`✅ CFB Debug: Added field: ${fieldName} (${fieldLabel})`);
                    } else {
                        console.log(`⚠️ CFB Debug: Excluded field: ${fieldName} (type: ${fieldType})`);
                    }
                } else {
                    console.log(`❌ CFB Debug: Skipped field - missing name or label`);
                }
            });

            console.log('🔍 CFB Debug: Total fields collected:', fields.length);
            return fields;
        }

        /**
         * Get icon for field type
         */
        getFieldIcon(type) {
            const icons = {
                'text': 'dashicons-edit',
                'number': 'dashicons-calculator',
                'slider': 'dashicons-leftright',
                'dropdown': 'dashicons-arrow-down-alt2',
                'radio': 'dashicons-marker',
                'checkbox': 'dashicons-yes',
                'calculation': 'dashicons-chart-line',
                'total': 'dashicons-money-alt'
            };

            return icons[type] || 'dashicons-admin-generic';
        }

        /**
         * Initialize field editor functionality
         */
        initFieldEditor() {
            // Click to insert formula items
            $(document).on('click', '.cfb-formula-item', (e) => {
                const item = $(e.currentTarget);
                const insertText = item.data('insert');
                const textarea = item.closest('.cfb-formula-builder').find('.cfb-formula-input');

                if (insertText && textarea.length) {
                    this.insertAtCursor(textarea[0], insertText);
                    textarea.focus();
                }
            });

            // Update available fields when fields change
            $(document).on('cfb-fields-updated', () => {
                this.updateAvailableFields();
            });

            // Initialize on page load
            this.updateAvailableFields();
        }

        /**
         * Insert text at cursor position in textarea
         */
        insertAtCursor(textarea, text) {
            const startPos = textarea.selectionStart;
            const endPos = textarea.selectionEnd;
            const value = textarea.value;

            textarea.value = value.substring(0, startPos) + text + value.substring(endPos);
            textarea.selectionStart = textarea.selectionEnd = startPos + text.length;

            // Trigger change event
            $(textarea).trigger('change');
        }

        /**
         * Update available fields in formula builder
         */
        updateAvailableFields() {
            const fieldsContainer = $('.cfb-available-fields');
            if (!fieldsContainer.length) return;

            const fields = this.getFormFields();

            if (fields.length === 0) {
                fieldsContainer.html('<p class="cfb-no-items">Add fields to see them here</p>');
                return;
            }

            let fieldsHtml = '';
            fields.forEach(field => {
                const icon = this.getFieldIcon(field.type);
                fieldsHtml += `
                    <div class="cfb-formula-item cfb-field-item" data-insert="{${field.name}}" title="${field.label}">
                        <span class="cfb-item-icon dashicons ${icon}"></span>
                        <div class="cfb-item-content">
                            <span class="cfb-item-name">${field.label}</span>
                            <span class="cfb-item-code">{${field.name}}</span>
                        </div>
                    </div>
                `;
            });

            fieldsContainer.html(fieldsHtml);
        }

        /**
         * Get current form fields
         */
        getFormFields() {
            const fields = [];

            $('.cfb-field-editor').each(function() {
                const fieldData = $(this).data('field-data');
                if (fieldData && fieldData.name && fieldData.label) {
                    // Exclude calculation and total fields from being referenced
                    if (!['calculation', 'total'].includes(fieldData.type)) {
                        fields.push({
                            name: fieldData.name,
                            label: fieldData.label,
                            type: fieldData.type
                        });
                    }
                }
            });

            return fields;
        }

        /**
         * Get icon for field type
         */
        getFieldIcon(type) {
            const icons = {
                'text': 'dashicons-edit',
                'number': 'dashicons-calculator',
                'slider': 'dashicons-leftright',
                'dropdown': 'dashicons-arrow-down-alt2',
                'radio': 'dashicons-marker',
                'checkbox': 'dashicons-yes',
                'calculation': 'dashicons-chart-line',
                'total': 'dashicons-money-alt'
            };

            return icons[type] || 'dashicons-admin-generic';
        }

        /**
         * Render global variables for formula builder
         */
        renderGlobalVariables() {
            // This will be populated via AJAX or from window.cfbVariables if available
            if (typeof window.cfbVariables !== 'undefined' && window.cfbVariables.length > 0) {
                return window.cfbVariables.map(variable => `
                    <div class="cfb-formula-item cfb-variable-item" data-insert="{${variable.name}}" title="${variable.description || ''}">
                        <span class="cfb-item-icon dashicons ${variable.icon}" style="color: ${variable.color}"></span>
                        <div class="cfb-item-content">
                            <span class="cfb-item-name">${variable.label}</span>
                            <span class="cfb-item-code">{${variable.name}}</span>
                            <span class="cfb-item-value">${variable.value}</span>
                        </div>
                    </div>
                `).join('');
            }

            return '<p class="cfb-no-items">No variables created yet. <a href="' +
                   (window.cfbAdminUrls?.variables || '#') + '">Create variables</a></p>';
        }

        /**
         * Render formula functions
         */
        renderFormulaFunctions() {
            const functions = [
                { name: 'SUM', syntax: 'SUM(field1, field2, ...)', description: 'Add multiple values together', icon: 'dashicons-plus-alt' },
                { name: 'MIN', syntax: 'MIN(field1, field2, ...)', description: 'Get the smallest value', icon: 'dashicons-arrow-down-alt' },
                { name: 'MAX', syntax: 'MAX(field1, field2, ...)', description: 'Get the largest value', icon: 'dashicons-arrow-up-alt' },
                { name: 'ROUND', syntax: 'ROUND(value, decimals)', description: 'Round to specified decimal places', icon: 'dashicons-marker' },
                { name: 'IF', syntax: 'condition ? value_if_true : value_if_false', description: 'Conditional logic', icon: 'dashicons-randomize' },
                { name: 'ABS', syntax: 'ABS(value)', description: 'Get absolute value', icon: 'dashicons-editor-expand' }
            ];

            return functions.map(func => `
                <div class="cfb-formula-item cfb-function-item" data-insert="${func.syntax}" title="${func.description}">
                    <span class="cfb-item-icon dashicons ${func.icon}"></span>
                    <div class="cfb-item-content">
                        <span class="cfb-item-name">${func.name}</span>
                        <span class="cfb-item-code">${func.name}</span>
                    </div>
                </div>
            `).join('');
        }

        /**
         * Render formula operators
         */
        renderFormulaOperators() {
            const operators = [
                { op: '+', label: 'Add', icon: 'dashicons-plus-alt' },
                { op: '-', label: 'Subtract', icon: 'dashicons-minus' },
                { op: '*', label: 'Multiply', icon: 'dashicons-dismiss' },
                { op: '/', label: 'Divide', icon: 'dashicons-editor-quote' },
                { op: '%', label: 'Modulo', icon: 'dashicons-admin-generic' },
                { op: '(', label: 'Open Parenthesis', icon: 'dashicons-editor-paragraph' },
                { op: ')', label: 'Close Parenthesis', icon: 'dashicons-editor-paragraph' },
                { op: '>', label: 'Greater Than', icon: 'dashicons-arrow-right-alt' },
                { op: '<', label: 'Less Than', icon: 'dashicons-arrow-left-alt' },
                { op: '==', label: 'Equal To', icon: 'dashicons-yes' },
                { op: '!=', label: 'Not Equal To', icon: 'dashicons-no' },
                { op: '>=', label: 'Greater or Equal', icon: 'dashicons-arrow-right-alt2' },
                { op: '<=', label: 'Less or Equal', icon: 'dashicons-arrow-left-alt2' }
            ];

            return operators.map(operator => `
                <div class="cfb-formula-item cfb-operator-item" data-insert=" ${operator.op} " title="${operator.label}">
                    <span class="cfb-item-icon dashicons ${operator.icon}"></span>
                    <div class="cfb-item-content">
                        <span class="cfb-item-name">${operator.label}</span>
                        <span class="cfb-item-code">${operator.op}</span>
                    </div>
                </div>
            `).join('');
        }

        /**
         * Render formula functions as beautiful icons
         */
        renderFormulaFunctionsAsIcons() {
            const functions = [
                { name: 'SUM', syntax: 'SUM(field1, field2, ...)', description: 'Add multiple values together', icon: 'dashicons-plus-alt', color: '#4caf50' },
                { name: 'MIN', syntax: 'MIN(field1, field2, ...)', description: 'Get the smallest value', icon: 'dashicons-arrow-down-alt', color: '#2196f3' },
                { name: 'MAX', syntax: 'MAX(field1, field2, ...)', description: 'Get the largest value', icon: 'dashicons-arrow-up-alt', color: '#ff9800' },
                { name: 'ROUND', syntax: 'ROUND(value, decimals)', description: 'Round to specified decimal places', icon: 'dashicons-marker', color: '#9c27b0' },
                { name: 'IF', syntax: 'condition ? value_if_true : value_if_false', description: 'Conditional logic', icon: 'dashicons-randomize', color: '#f44336' },
                { name: 'ABS', syntax: 'ABS(value)', description: 'Get absolute value', icon: 'dashicons-editor-expand', color: '#607d8b' }
            ];

            return functions.map(func => `
                <div class="cfb-function-icon" data-insert="${func.syntax}" title="${func.description}">
                    <div class="cfb-function-icon-circle" style="background-color: ${func.color}">
                        <span class="dashicons ${func.icon}"></span>
                    </div>
                    <span class="cfb-function-name">${func.name}</span>
                </div>
            `).join('');
        }

        /**
         * Render formula operators as beautiful icons
         */
        renderFormulaOperatorsAsIcons() {
            const operators = [
                { op: '+', label: 'Add', icon: 'dashicons-plus-alt', color: '#4caf50' },
                { op: '-', label: 'Subtract', icon: 'dashicons-minus', color: '#f44336' },
                { op: '*', label: 'Multiply', icon: 'dashicons-dismiss', color: '#ff9800' },
                { op: '/', label: 'Divide', icon: 'dashicons-editor-quote', color: '#2196f3' },
                { op: '(', label: 'Open Parenthesis', icon: 'dashicons-editor-paragraph', color: '#9c27b0' },
                { op: ')', label: 'Close Parenthesis', icon: 'dashicons-editor-paragraph', color: '#9c27b0' },
                { op: '>', label: 'Greater Than', icon: 'dashicons-arrow-right-alt', color: '#607d8b' },
                { op: '<', label: 'Less Than', icon: 'dashicons-arrow-left-alt', color: '#607d8b' },
                { op: '==', label: 'Equal To', icon: 'dashicons-yes', color: '#4caf50' },
                { op: '!=', label: 'Not Equal To', icon: 'dashicons-no', color: '#f44336' }
            ];

            return operators.map(operator => `
                <div class="cfb-operator-icon" data-insert=" ${operator.op} " title="${operator.label}">
                    <div class="cfb-operator-icon-circle" style="background-color: ${operator.color}">
                        <span class="dashicons ${operator.icon}"></span>
                    </div>
                    <span class="cfb-operator-name">${operator.op}</span>
                </div>
            `).join('');
        }
    }

    // Initialize when document is ready
    $(document).ready(() => {
        if ($('.cfb-form-builder').length || $('.cfb-settings-page').length) {
            window.cfbFormBuilder = new CFBFormBuilder();
        }

        // Add global function for debugging
        window.cfbDebugFields = function() {
            console.log('🔍 CFB Debug: Manual field update triggered');
            if (window.cfbFormBuilder) {
                window.cfbFormBuilder.updateAvailableFields();
            } else {
                console.log('❌ CFB Debug: Form builder not found');
            }
        };

        // Shortcode copy functionality
        $('.cfb-shortcode').on('click', function() {
            const text = $(this).text();
            navigator.clipboard.writeText(text).then(() => {
                $(this).css('background', '#d4edda').delay(1000).queue(function() {
                    $(this).css('background', '#f6f7f7').dequeue();
                });
            });
        });
    });

})(jQuery);
