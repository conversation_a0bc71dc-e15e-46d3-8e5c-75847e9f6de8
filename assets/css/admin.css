/* CFB Calculator Admin Styles */

/* Form Builder Layout */
.cfb-form-builder {
    background: #f1f1f1;
    margin: 20px 0;
}

.cfb-builder-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 20px;
    margin-top: 20px;
    min-height: 600px;
}

/* Left Sidebar with Tabs */
.cfb-left-sidebar {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
}

/* Tab Navigation */
.cfb-tab-nav {
    display: flex;
    background: #f1f1f1;
    border-bottom: 1px solid #ccd0d4;
}

.cfb-tab-button {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    color: #555;
    transition: all 0.2s ease;
    border-right: 1px solid #ccd0d4;
}

.cfb-tab-button:last-child {
    border-right: none;
}

.cfb-tab-button:hover {
    background: #e8e8e8;
    color: #333;
}

.cfb-tab-button.active {
    background: #fff;
    color: #0073aa;
    border-bottom: 2px solid #0073aa;
    margin-bottom: -1px;
}

/* Tab Content */
.cfb-tab-content {
    padding: 20px;
    display: none;
}

.cfb-tab-content.active {
    display: block;
}

/* Settings Panel (now in tab) */
.cfb-settings-content {
    /* Settings styles */
}

/* Field Types Panel (now in tab) */
.cfb-field-types-content {
    /* Field types styles */
}

.cfb-field-types-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    margin-bottom: 20px;
}

.cfb-field-type {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cfb-field-type:hover {
    background: #e3f2fd;
    border-color: #2196F3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

.cfb-field-type .dashicons {
    font-size: 18px;
    color: #666;
}

.cfb-field-type:hover .dashicons {
    color: #2196F3;
}

.cfb-field-type-label {
    font-size: 13px;
    font-weight: 500;
    color: #333;
}

.cfb-field-types-help {
    background: #f0f6fc;
    border: 1px solid #c8e1ff;
    border-radius: 6px;
    padding: 15px;
}

.cfb-field-types-help h4 {
    margin: 0 0 10px 0;
    color: #0969da;
    font-size: 13px;
}

.cfb-field-types-help ul {
    margin: 0;
    padding-left: 16px;
}

.cfb-field-types-help li {
    font-size: 12px;
    color: #656d76;
    margin-bottom: 4px;
}

/* Form Builder Canvas - Much Wider */
.cfb-builder-canvas {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
}

.cfb-canvas-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ccd0d4;
}

.cfb-canvas-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.cfb-canvas-actions {
    display: flex;
    gap: 10px;
}

.cfb-canvas-actions .button {
    background: rgba(255,255,255,0.2);
    color: #fff;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.cfb-canvas-actions .button:hover {
    background: rgba(255,255,255,0.3);
}

.cfb-canvas-actions .button-primary {
    background: rgba(255,255,255,0.9);
    color: #667eea;
    border: 1px solid rgba(255,255,255,0.9);
}

.cfb-canvas-actions .button-primary:hover {
    background: #fff;
}

.cfb-form-fields {
    flex: 1;
    padding: 20px;
    min-height: 400px;
}

.cfb-drop-zone {
    margin: 20px;
    border: 2px dashed #ccd0d4;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background: #f9f9f9;
    transition: all 0.2s ease;
}

.cfb-drop-zone:hover {
    border-color: #2196F3;
    background: #f0f8ff;
}

.cfb-drop-zone-content .dashicons {
    font-size: 48px;
    color: #ccd0d4;
    margin-bottom: 15px;
}

.cfb-drop-zone-content h4 {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 16px;
}

.cfb-drop-zone-content p {
    margin: 0;
    color: #999;
    font-size: 14px;
}

/* Checkbox styling */
.cfb-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    cursor: pointer;
}

.cfb-checkbox-text {
    font-size: 13px;
    color: #333;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .cfb-builder-container {
        grid-template-columns: 260px 1fr;
    }
}

@media (max-width: 768px) {
    .cfb-builder-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .cfb-left-sidebar {
        order: 2;
    }

    .cfb-builder-canvas {
        order: 1;
    }

    .cfb-canvas-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .cfb-canvas-actions {
        justify-content: center;
    }

    .cfb-tab-nav {
        flex-direction: column;
    }

    .cfb-tab-button {
        border-right: none;
        border-bottom: 1px solid #ccd0d4;
    }

    .cfb-tab-button:last-child {
        border-bottom: none;
    }
}

@media (max-width: 480px) {
    .cfb-form-builder {
        margin: 10px 0;
    }

    .cfb-builder-container {
        gap: 10px;
    }

    .cfb-tab-content {
        padding: 15px;
    }

    .cfb-canvas-header {
        padding: 12px 15px;
    }

    .cfb-form-fields {
        padding: 15px;
    }

    .cfb-drop-zone {
        margin: 15px;
        padding: 30px 15px;
    }
}

/* Enhanced Formula Builder Styles */
.cfb-formula-setting {
    margin-bottom: 20px;
}

.cfb-formula-builder {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    background: #fff;
    overflow: hidden;
}

.cfb-formula-input {
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border: none;
    border-bottom: 1px solid #e1e5e9;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    resize: vertical;
    direction: ltr !important;
    text-align: left !important;
}

.cfb-formula-input:focus {
    outline: none;
    box-shadow: inset 0 0 0 2px #2196F3;
}

.cfb-formula-helpers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1px;
    background: #e1e5e9;
}

.cfb-formula-section {
    background: #fff;
    padding: 15px;
}

.cfb-formula-section h4 {
    margin: 0 0 12px 0;
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 6px;
}

.cfb-formula-section h4 .dashicons {
    font-size: 16px;
    color: #6c757d;
}

.cfb-formula-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
    max-height: 200px;
    overflow-y: auto;
}

.cfb-formula-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 10px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.cfb-formula-item:hover {
    background: #e3f2fd;
    border-color: #2196F3;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.15);
}

.cfb-formula-item.cfb-variable-item:hover {
    background: #f3e5f5;
    border-color: #9c27b0;
}

.cfb-formula-item.cfb-function-item:hover {
    background: #e8f5e8;
    border-color: #4caf50;
}

.cfb-formula-item.cfb-operator-item:hover {
    background: #fff3e0;
    border-color: #ff9800;
}

.cfb-item-icon {
    font-size: 14px;
    color: #6c757d;
    flex-shrink: 0;
}

.cfb-variable-item .cfb-item-icon {
    color: inherit;
}

.cfb-function-item .cfb-item-icon {
    color: #4caf50;
}

.cfb-operator-item .cfb-item-icon {
    color: #ff9800;
}

.cfb-item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
}

.cfb-item-name {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.cfb-item-code {
    font-family: 'Courier New', monospace;
    color: #6c757d;
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.cfb-item-value {
    font-weight: 600;
    color: #28a745;
    font-size: 11px;
}

.cfb-no-items {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 20px;
    margin: 0;
}

.cfb-no-items a {
    color: #2196F3;
    text-decoration: none;
}

.cfb-no-items a:hover {
    text-decoration: underline;
}

.cfb-formula-help {
    padding: 12px 15px;
    background: #f0f6fc;
    border-top: 1px solid #e1e5e9;
}

.cfb-formula-help small {
    color: #0969da;
    font-size: 12px;
    line-height: 1.4;
}

/* Responsive Formula Builder */
@media (max-width: 768px) {
    .cfb-formula-helpers {
        grid-template-columns: 1fr;
    }

    .cfb-formula-section {
        padding: 12px;
    }

    .cfb-formula-items {
        max-height: 150px;
    }

    .cfb-formula-item {
        padding: 6px 8px;
    }
}

/* Formula Builder Animation */
.cfb-formula-item {
    animation: cfbFadeInUp 0.3s ease;
}

@keyframes cfbFadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom Scrollbar for Formula Items */
.cfb-formula-items::-webkit-scrollbar {
    width: 6px;
}

.cfb-formula-items::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.cfb-formula-items::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.cfb-formula-items::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Enhanced Field Controls */
.cfb-field-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
    cursor: pointer;
}

.cfb-field-move-controls {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.cfb-move-field-up,
.cfb-move-field-down {
    background: none;
    border: none;
    padding: 2px 4px;
    cursor: pointer;
    color: #6c757d;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.cfb-move-field-up:hover,
.cfb-move-field-down:hover {
    background: #e9ecef;
    color: #495057;
}

.cfb-field-title {
    flex: 1;
    font-weight: 600;
    color: #495057;
}

.cfb-field-actions {
    display: flex;
    gap: 5px;
}

.cfb-toggle-field,
.cfb-edit-field,
.cfb-delete-field {
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
    color: #6c757d;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.cfb-toggle-field:hover {
    background: #e3f2fd;
    color: #2196F3;
}

.cfb-edit-field:hover {
    background: #fff3e0;
    color: #ff9800;
}

.cfb-delete-field:hover {
    background: #ffebee;
    color: #f44336;
}

.cfb-toggle-field.active .dashicons {
    transform: rotate(180deg);
}

/* Enhanced Field Settings Grid */
.cfb-settings-grid {
    padding: 15px;
}

.cfb-settings-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.cfb-settings-row .cfb-form-group {
    margin-bottom: 0;
}

.cfb-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 600;
    color: #495057;
}

.cfb-checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* Beautiful Function Icons Bar */
.cfb-formula-functions-bar {
    padding: 15px;
    background: #f8f9fa;
    border-top: 1px solid #e1e5e9;
}

.cfb-formula-functions-bar h4 {
    margin: 0 0 12px 0;
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 6px;
}

.cfb-functions-icons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.cfb-function-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 8px;
}

.cfb-function-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.cfb-function-icon-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.cfb-function-icon:hover .cfb-function-icon-circle {
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
}

.cfb-function-icon-circle .dashicons {
    font-size: 20px;
    color: white;
    line-height: 1;
}

.cfb-function-name {
    font-size: 11px;
    font-weight: 600;
    color: #495057;
    text-align: center;
}

.cfb-function-icon:hover .cfb-function-name {
    color: #2196F3;
}

/* Beautiful Operator Icons */
.cfb-operators-icons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding: 8px 0;
}

.cfb-operator-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 6px;
    border-radius: 6px;
}

.cfb-operator-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.cfb-operator-icon-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.cfb-operator-icon:hover .cfb-operator-icon-circle {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.cfb-operator-icon-circle .dashicons {
    font-size: 14px;
    color: white;
    line-height: 1;
}

.cfb-operator-name {
    font-size: 10px;
    font-weight: 600;
    color: #495057;
    text-align: center;
    min-height: 12px;
}

.cfb-operator-icon:hover .cfb-operator-name {
    color: #2196F3;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .cfb-settings-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .cfb-formula-main {
        flex-direction: column;
    }

    .cfb-formula-variables-sidebar {
        flex-direction: row;
    }

    .cfb-formula-variables-sidebar .cfb-formula-section {
        flex: 1;
    }

    .cfb-formula-functions-row {
        grid-template-columns: 1fr;
    }

    .cfb-field-header {
        padding: 10px 12px;
    }

    .cfb-field-move-controls {
        flex-direction: row;
    }
}

.cfb-settings-panel h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.cfb-form-group {
    margin-bottom: 20px;
}

.cfb-form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #23282d;
}

.cfb-form-group input[type="text"],
.cfb-form-group textarea,
.cfb-form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.cfb-form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.cfb-form-group input[type="checkbox"] {
    margin-right: 8px;
}

/* Field Palette */
.cfb-field-palette {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    height: fit-content;
}

.cfb-field-palette h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.cfb-field-types {
    display: grid;
    gap: 10px;
}

.cfb-field-type {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s ease;
}

.cfb-field-type:hover {
    background: #e8f4fd;
    border-color: #0073aa;
}

.cfb-field-type:active {
    cursor: grabbing;
}

.cfb-field-type .dashicons {
    color: #0073aa;
    font-size: 18px;
}

.cfb-field-type .label {
    font-size: 13px;
    font-weight: 500;
}

/* Builder Canvas */
.cfb-builder-canvas {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    min-height: 500px;
}

.cfb-builder-canvas h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.cfb-form-fields {
    min-height: 300px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.cfb-drop-zone {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.cfb-drop-zone.drag-over {
    border-color: #0073aa;
    background: #e8f4fd;
}

/* Field Editor */
.cfb-field-editor {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
}

.cfb-field-editor:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cfb-field-editor.active {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.cfb-field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #fff;
    border-bottom: 1px solid #ddd;
    cursor: move;
}

.cfb-field-title {
    font-weight: 600;
    color: #23282d;
}

.cfb-field-actions {
    display: flex;
    gap: 5px;
}

.cfb-field-actions button {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    border-radius: 2px;
    color: #666;
}

.cfb-field-actions button:hover {
    background: #f0f0f0;
    color: #0073aa;
}

.cfb-field-preview {
    padding: 15px;
}

.cfb-field-settings {
    padding: 15px;
    background: #f6f7f7;
    border-top: 1px solid #ddd;
}

/* Calculation Panel */
.cfb-calculation-panel {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    height: fit-content;
}

.cfb-calculation-panel h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.cfb-subtotals-section h4,
.cfb-total-section h4 {
    color: #23282d;
    margin-bottom: 15px;
}

.cfb-subtotal-editor {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.cfb-subtotal-editor .cfb-form-group {
    margin-bottom: 15px;
}

.cfb-subtotal-editor .cfb-form-group:last-child {
    margin-bottom: 0;
}

.cfb-remove-subtotal {
    background: #dc3545;
    color: #fff;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.cfb-remove-subtotal:hover {
    background: #c82333;
}

#add-subtotal {
    background: #28a745;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

#add-subtotal:hover {
    background: #218838;
}

#total-formula {
    width: 100%;
    min-height: 80px;
    font-family: monospace;
    font-size: 13px;
}

/* Form Actions */
.cfb-form-actions {
    margin-top: 30px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    text-align: center;
}

.cfb-form-actions .button {
    margin: 0 5px;
}

/* Field Settings Modal */
.cfb-field-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: none;
}

.cfb-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 4px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.cfb-modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cfb-modal-title {
    margin: 0;
    font-size: 18px;
    color: #23282d;
}

.cfb-modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
}

.cfb-modal-body {
    padding: 20px;
}

.cfb-modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    text-align: right;
}

/* Conditional Logic Builder */
.cfb-conditional-logic {
    background: #f6f7f7;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.cfb-conditional-settings {
    margin-bottom: 15px;
}

.cfb-conditional-settings .cfb-form-group {
    margin-bottom: 15px;
}

.cfb-conditional-settings label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

.conditional-logic-type {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.cfb-conditions-list {
    margin-bottom: 15px;
}

.cfb-condition-row {
    display: grid;
    grid-template-columns: 1fr 120px 1fr 30px;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
    padding: 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.cfb-condition-row:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cfb-condition-field select,
.cfb-condition-operator select,
.cfb-condition-value input,
.cfb-condition-value select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
}

.cfb-condition-actions {
    text-align: center;
}

.cfb-remove-condition {
    background: #dc3545;
    color: #fff;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.cfb-remove-condition:hover {
    background: #c82333;
    transform: scale(1.1);
}

.cfb-add-condition {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.cfb-add-condition:hover {
    background: #005a87;
}

/* Conditional Logic States */
.cfb-condition-value[style*="display: none"] {
    display: none !important;
}

/* Conditional Logic Responsive */
@media (max-width: 768px) {
    .cfb-condition-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .cfb-condition-actions {
        text-align: right;
    }
}

/* Options Editor */
.cfb-options-editor {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.cfb-option-item {
    display: grid;
    grid-template-columns: 1fr 1fr auto auto;
    gap: 10px;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    background: #fff;
}

.cfb-option-item:last-child {
    border-bottom: none;
}

.cfb-option-item input {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.cfb-add-option {
    background: #28a745;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

.cfb-remove-option {
    background: #dc3545;
    color: #fff;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
}

/* Responsive Design */
@media (max-width: 1400px) {
    .cfb-builder-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .cfb-settings-panel,
    .cfb-field-palette,
    .cfb-calculation-panel {
        order: 1;
    }
    
    .cfb-builder-canvas {
        order: 2;
    }
}

@media (max-width: 768px) {
    .cfb-builder-container {
        margin: 10px;
    }
    
    .cfb-field-types {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cfb-condition {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .cfb-option-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }
}

/* Loading States */
.cfb-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.cfb-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.cfb-message {
    padding: 12px 16px;
    border-radius: 4px;
    margin: 15px 0;
    font-size: 14px;
}

.cfb-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cfb-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cfb-message.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Formula Builder Styles */
.cfb-formula-builder {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    margin-top: 15px;
}

.cfb-formula-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cfb-formula-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.cfb-formula-actions {
    display: flex;
    gap: 10px;
}

.cfb-formula-actions .button {
    background: rgba(255,255,255,0.2);
    color: #fff;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cfb-formula-actions .button:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
}

.cfb-formula-editor {
    position: relative;
    padding: 20px;
    background: #f9f9f9;
}

.cfb-formula-input {
    width: 100%;
    min-height: 120px;
    padding: 15px;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    background: #fff;
    resize: vertical;
    transition: border-color 0.2s ease;
    position: relative;
    z-index: 2;
}

.cfb-formula-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.cfb-formula-overlay {
    position: absolute;
    top: 35px;
    left: 35px;
    right: 35px;
    bottom: 35px;
    padding: 15px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    pointer-events: none;
    z-index: 1;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
}

.cfb-field-highlight {
    background: rgba(46, 160, 67, 0.2);
    color: #2ea043;
    padding: 1px 2px;
    border-radius: 2px;
}

.cfb-function-highlight {
    background: rgba(3, 102, 214, 0.2);
    color: #0366d6;
    font-weight: 600;
}

.cfb-operator-highlight {
    color: #d73a49;
    font-weight: 600;
}

.cfb-number-highlight {
    color: #005cc5;
}

.cfb-formula-toolbar {
    padding: 20px;
    background: #fff;
    border-top: 1px solid #e1e1e1;
}

.cfb-toolbar-section {
    margin-bottom: 20px;
}

.cfb-toolbar-section:last-child {
    margin-bottom: 0;
}

.cfb-toolbar-section label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 13px;
}

.cfb-toolbar-items {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.cfb-insert-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
}

.cfb-insert-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.cfb-field-btn {
    background: #e8f5e8;
    border-color: #c3e6c3;
    color: #2ea043;
}

.cfb-field-btn:hover {
    background: #d4edda;
    border-color: #a3d9a3;
}

.cfb-function-btn {
    background: #e3f2fd;
    border-color: #bbdefb;
    color: #0366d6;
}

.cfb-function-btn:hover {
    background: #d1ecf1;
    border-color: #9bd4ed;
}

.cfb-operator-btn {
    background: #ffeaa7;
    border-color: #fdcb6e;
    color: #e17055;
    font-weight: 600;
}

.cfb-operator-btn:hover {
    background: #fdcb6e;
    border-color: #f39c12;
}

.cfb-variable-btn {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.cfb-variable-btn:hover {
    background: #f1b0b7;
    border-color: #e2a6ad;
}

.cfb-formula-help {
    background: #f8f9fa;
    border-top: 1px solid #e1e1e1;
}

.cfb-help-toggle {
    padding: 15px 20px;
    border-bottom: 1px solid #e1e1e1;
}

.cfb-toggle-help {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cfb-toggle-help:hover {
    color: #495057;
}

.cfb-help-content {
    padding: 20px;
}

.cfb-help-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.cfb-help-section h5 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
}

.cfb-help-section ul {
    margin: 0;
    padding-left: 20px;
}

.cfb-help-section li {
    margin-bottom: 5px;
    font-size: 13px;
    line-height: 1.4;
}

.cfb-help-section code {
    background: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #d73a49;
}

.cfb-formula-validation {
    padding: 15px 20px;
    border-top: 1px solid #e1e1e1;
}

.cfb-validation-result {
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
}

.cfb-validation-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cfb-validation-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cfb-validation-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Field Formula Builder */
.cfb-field-formula-builder {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-top: 15px;
    overflow: hidden;
}

.cfb-field-formula-builder .cfb-formula-header {
    background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
    color: #fff;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cfb-field-formula-builder .cfb-formula-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cfb-field-formula-builder .cfb-formula-actions {
    display: flex;
    gap: 8px;
}

.cfb-field-formula-builder .cfb-formula-actions .button-secondary {
    background: rgba(255,255,255,0.2);
    color: #fff;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.cfb-formula-workspace {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 0;
}

.cfb-formula-editor {
    padding: 16px;
    background: #f9f9f9;
}

.cfb-formula-editor label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.cfb-formula-input {
    width: 100%;
    min-height: 80px;
    max-height: 200px;
    padding: 12px;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    background: #fff;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.cfb-formula-input:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.cfb-formula-validation {
    margin-top: 8px;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* Operators Bar Under Formula */
.cfb-operators-bar {
    margin-top: 16px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 6px;
}

.cfb-operators-bar h5 {
    margin: 0 0 10px 0;
    font-size: 12px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cfb-operators-bar .cfb-operators-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 8px;
    max-width: 100%;
}

.cfb-validation-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cfb-validation-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cfb-validation-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.cfb-formula-tools {
    background: #fff;
    border-left: 1px solid #e1e1e1;
    padding: 16px;
    overflow-y: auto;
    max-height: 400px;
}

.cfb-tool-section {
    margin-bottom: 20px;
}

.cfb-tool-section:last-child {
    margin-bottom: 0;
}

.cfb-tool-section h5 {
    margin: 0 0 10px 0;
    font-size: 12px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cfb-fields-grid,
.cfb-functions-grid,
.cfb-operators-grid,
.cfb-calc-fields-grid {
    display: grid;
    gap: 6px;
}

.cfb-fields-grid {
    grid-template-columns: 1fr;
}

.cfb-functions-grid {
    grid-template-columns: 1fr;
}

.cfb-operators-grid {
    grid-template-columns: repeat(3, 1fr);
}

.cfb-field-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 10px;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.cfb-field-btn:hover {
    background: #e3f2fd;
    border-color: #2196F3;
    transform: translateY(-1px);
}

.cfb-field-name {
    font-size: 12px;
    font-weight: 600;
    color: #2c3e50;
}

.cfb-field-type {
    font-size: 10px;
    color: #6c757d;
    text-transform: uppercase;
}

.cfb-function-btn {
    background: #fff3e0;
    border: 1px solid #ffcc02;
    border-radius: 4px;
    padding: 6px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.cfb-function-btn:hover {
    background: #ffe0b2;
    border-color: #ff9800;
}

.cfb-func-name {
    display: block;
    font-size: 11px;
    font-weight: 600;
    color: #e65100;
}

.cfb-func-desc {
    display: block;
    font-size: 9px;
    color: #bf360c;
}

.cfb-operator-btn {
    background: #f3e5f5;
    border: 1px solid #ce93d8;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    font-weight: 600;
    color: #7b1fa2;
}

.cfb-operator-btn:hover {
    background: #e1bee7;
    border-color: #9c27b0;
}

.cfb-no-fields {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 20px;
    margin: 0;
}

.cfb-calc-note {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 4px;
    padding: 8px;
    margin: 0 0 10px 0;
    font-size: 11px;
    color: #1565c0;
    display: flex;
    align-items: center;
    gap: 6px;
}

.cfb-formula-help {
    border-top: 1px solid #e1e1e1;
    background: #f8f9fa;
}

.cfb-formula-help details {
    padding: 12px 16px;
}

.cfb-formula-help summary {
    cursor: pointer;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.cfb-help-content {
    margin-top: 12px;
}

.cfb-help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.cfb-help-section h6 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.cfb-help-section ul {
    margin: 0;
    padding-left: 16px;
}

.cfb-help-section li {
    margin-bottom: 4px;
    font-size: 11px;
    line-height: 1.3;
}

.cfb-help-section code {
    background: #f1f3f4;
    padding: 1px 3px;
    border-radius: 2px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 10px;
    color: #d73a49;
}

/* Responsive Formula Builder */
@media (max-width: 768px) {
    .cfb-formula-workspace {
        grid-template-columns: 1fr;
    }

    .cfb-formula-tools {
        border-left: none;
        border-top: 1px solid #e1e1e1;
        max-height: 300px;
    }

    .cfb-help-grid {
        grid-template-columns: 1fr;
    }

    .cfb-operators-grid {
        grid-template-columns: repeat(5, 1fr);
    }

    /* Operators bar responsive adjustments */
    .cfb-operators-bar {
        margin-top: 12px;
        padding: 10px;
    }

    .cfb-operators-bar .cfb-operators-grid {
        grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
        gap: 6px;
    }
}

/* Field Layout Controls */
.cfb-layout-controls {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin-top: 8px;
}

.cfb-layout-row {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 10px;
    align-items: center;
    margin-bottom: 8px;
}

.cfb-layout-row:last-child {
    margin-bottom: 0;
}

.cfb-layout-row label {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.cfb-layout-row select {
    padding: 4px 8px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    font-size: 12px;
    background: #fff;
}

/* Field Width Classes for Frontend */
.cfb-field-width-full {
    width: 100%;
}

.cfb-field-width-half {
    width: 50%;
}

.cfb-field-width-third {
    width: 33.333%;
}

.cfb-field-width-quarter {
    width: 25%;
}

.cfb-field-position-left {
    margin-left: 0;
    margin-right: auto;
}

.cfb-field-position-center {
    margin-left: auto;
    margin-right: auto;
}

.cfb-field-position-right {
    margin-left: auto;
    margin-right: 0;
}

/* One-click field addition */
.cfb-field-type {
    cursor: pointer;
    transition: all 0.2s ease;
}

.cfb-field-type:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.cfb-field-type:active {
    transform: translateY(0);
}

/* Field type icons */
.cfb-field-type .dashicons {
    font-size: 20px;
    margin-bottom: 5px;
}

/* Improved field editor header */
.cfb-field-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.cfb-field-title {
    font-weight: 600;
    color: #495057;
}

/* Field preview improvements */
.cfb-field-preview {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
}

/* Settings panel improvements */
.cfb-field-settings {
    background: #f8f9fa;
}

.cfb-field-settings h4 {
    color: #495057;
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #dee2e6;
}

/* Responsive layout controls */
@media (max-width: 768px) {
    .cfb-layout-row {
        grid-template-columns: 1fr;
        gap: 4px;
    }

    .cfb-layout-row label {
        font-size: 11px;
    }
}

/* Drag and Drop States */
.cfb-field-editor.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.cfb-drop-zone.drag-over {
    background: #e8f4fd;
    border-color: #0073aa;
}

.cfb-field-editor.drop-target {
    border-top: 3px solid #0073aa;
}
